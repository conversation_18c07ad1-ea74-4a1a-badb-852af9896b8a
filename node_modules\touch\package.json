{"_args": [["touch@3.1.1", "C:\\chemistry\\portal"]], "_development": true, "_from": "touch@3.1.1", "_id": "touch@3.1.1", "_inBundle": false, "_integrity": "sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==", "_location": "/touch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "touch@3.1.1", "name": "touch", "escapedName": "touch", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/nodemon"], "_resolved": "https://registry.npmmirror.com/touch/-/touch-3.1.1.tgz", "_spec": "3.1.1", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"nodetouch": "bin/nodetouch.js"}, "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "description": "like touch(1) in node", "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "files": ["index.js", "bin/nodetouch.js"], "homepage": "https://github.com/isaacs/node-touch#readme", "license": "ISC", "name": "touch", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100 -J"}, "version": "3.1.1"}