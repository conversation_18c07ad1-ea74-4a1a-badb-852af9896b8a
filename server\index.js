// 导入 Express 框架
import express from 'express'
// 导入 gzip 压缩中间件
import compression from 'compression'
// 导入静态文件服务中间件
import serveStatic from 'serve-static'
// 导入文件 URL 转换工具
import { fileURLToPath } from 'url'
// 导入路径处理工具
import { dirname, resolve } from 'path'
// 导入文件系统模块
import fs from 'fs'

// 获取当前文件的目录路径
const __dirname = dirname(fileURLToPath(import.meta.url))
// 判断是否为生产环境
const isProduction = process.env.NODE_ENV === 'production'
// 设置端口号，默认为 3000
const port = process.env.PORT || 3000

// 创建服务器的异步函数
async function createServer() {
  // 创建 Express 应用实例
  const app = express()

  // 启用 gzip 压缩中间件
  app.use(compression())

  // 声明 Vite 开发服务器变量
  let vite
  // 如果不是生产环境
  if (!isProduction) {
    // 开发模式：使用 Vite 的开发服务器
    // 动态导入 Vite 的创建服务器函数
    const { createServer: createViteServer } = await import('vite')
    // 创建 Vite 开发服务器
    vite = await createViteServer({
      server: { middlewareMode: true }, // 设置为中间件模式
      appType: 'custom' // 设置应用类型为自定义
    })
    // 使用 Vite 中间件
    app.use(vite.middlewares)
  } else {
    // 生产模式：提供静态文件服务
    // 配置静态文件服务，指向构建后的客户端文件夹
    app.use(serveStatic(resolve(__dirname, '../dist/client'), {
      index: false // 不自动提供 index.html
    }))
  }

  // SSR（服务端渲染）处理中间件
  // 匹配所有路由
  app.use('*', async (req, res, next) => {
    // 获取请求的原始 URL
    const url = req.originalUrl

    try {
      // 声明模板和渲染函数变量
      let template, render
      
      // 如果不是生产环境
      if (!isProduction) {
        // 开发模式：从 Vite 获取模板和渲染函数
        // 同步读取 HTML 模板文件
        template = fs.readFileSync(
          resolve(__dirname, '../index.html'),
          'utf-8'
        )
        // 通过 Vite 转换 HTML 模板
        template = await vite.transformIndexHtml(url, template)
        // 通过 Vite 加载服务端入口模块并获取渲染函数
        render = (await vite.ssrLoadModule('/src/entry-server.js')).render
      } else {
        // 生产模式：使用预构建的文件
        // 读取构建后的 HTML 模板文件
        template = fs.readFileSync(
          resolve(__dirname, '../dist/client/index.html'),
          'utf-8'
        )
        // 导入构建后的服务端入口文件并获取渲染函数
        render = (await import('../dist/server/entry-server.js')).render
      }

      // 渲染应用，获取 HTML 字符串
      const { html: appHtml } = await render(url)
      
      // 将渲染的应用 HTML 插入到模板中的占位符位置
      const html = template.replace('<!--ssr-outlet-->', appHtml)
      
      // 设置响应状态码为 200，内容类型为 HTML，并发送渲染后的 HTML
      res.status(200).set({ 'Content-Type': 'text/html' }).end(html)
    } catch (e) {
      // 错误处理
      // 如果是开发模式，让 Vite 修复堆栈跟踪信息
      if (!isProduction) {
        vite.ssrFixStacktrace(e)
      }
      // 在控制台输出错误信息
      console.error(e)
      // 返回 500 错误状态码和错误消息
      res.status(500).end(e.message)
    }
  })

  // 返回应用实例和 Vite 实例
  return { app, vite }
}

// 创建服务器并启动
createServer().then(({ app }) => {
  // 监听指定端口
  app.listen(port, () => {
    // 输出服务器启动成功信息
    console.log(`🚀 服务器运行在 http://localhost:${port}`)
  })
})

