{"_args": [["semver@7.0.0", "C:\\chemistry\\portal"]], "_development": true, "_from": "semver@7.0.0", "_id": "semver@7.0.0", "_inBundle": false, "_integrity": "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==", "_location": "/simple-update-notifier/semver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "semver@7.0.0", "name": "semver", "escapedName": "semver", "rawSpec": "7.0.0", "saveSpec": null, "fetchSpec": "7.0.0"}, "_requiredBy": ["/simple-update-notifier"], "_resolved": "https://registry.npmmirror.com/semver/-/semver-7.0.0.tgz", "_spec": "7.0.0", "_where": "C:\\chemistry\\portal", "bin": {"semver": "bin/semver.js"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^14.10.1"}, "files": ["bin", "range.bnf", "classes", "functions", "internal", "ranges", "index.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "index.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "snap": "tap", "test": "tap"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "version": "7.0.0"}