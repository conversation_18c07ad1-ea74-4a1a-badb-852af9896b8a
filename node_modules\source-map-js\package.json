{"_args": [["source-map-js@1.2.1", "C:\\chemistry\\portal"]], "_from": "source-map-js@1.2.1", "_id": "source-map-js@1.2.1", "_inBundle": false, "_integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "_location": "/source-map-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "source-map-js@1.2.1", "name": "source-map-js", "escapedName": "source-map-js", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-sfc", "/postcss"], "_resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "_spec": "1.2.1", "_where": "C:\\chemistry\\portal", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "clean-publish": {"cleanDocs": true}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generates and consumes source maps", "devDependencies": {"clean-publish": "^3.1.0", "doctoc": "^0.15.0", "webpack": "^1.12.0"}, "engines": {"node": ">=0.10.0"}, "files": ["source-map.js", "source-map.d.ts", "lib/"], "homepage": "https://github.com/7rulnik/source-map-js", "license": "BSD-3-<PERSON><PERSON>", "main": "./source-map.js", "name": "source-map-js", "repository": {"type": "git", "url": "git+https://github.com/7rulnik/source-map-js.git"}, "scripts": {"build": "webpack --color", "test": "npm run build && node test/run-tests.js", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "typings": "source-map.d.ts", "version": "1.2.1"}