{"_from": "utils-merge@1.0.1", "_id": "utils-merge@1.0.1", "_inBundle": false, "_integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "_location": "/utils-merge", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "utils-merge@1.0.1", "name": "utils-merge", "escapedName": "utils-merge", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz", "_shasum": "9f95710f50a267947b2ccc124741c1028427e713", "_spec": "utils-merge@1.0.1", "_where": "C:\\chemistry\\portal\\node_modules\\express", "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "bugs": {"url": "http://github.com/jaredhanson/utils-merge/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "merge() utility function", "devDependencies": {"chai": "1.x.x", "make-node": "0.3.x", "mocha": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/jaredhanson/utils-merge#readme", "keywords": ["util"], "license": "MIT", "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "main": "./index", "name": "utils-merge", "repository": {"type": "git", "url": "git://github.com/jaredhanson/utils-merge.git"}, "scripts": {"test": "mocha --reporter spec --require test/bootstrap/node test/*.test.js"}, "version": "1.0.1"}