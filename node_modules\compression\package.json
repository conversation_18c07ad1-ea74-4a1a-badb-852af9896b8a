{"_args": [["compression@1.8.0", "C:\\chemistry\\portal"]], "_from": "compression@1.8.0", "_id": "compression@1.8.0", "_inBundle": false, "_integrity": "sha512-k6WLKfunuqCYD3t6AsuPGvQWaKwuLLh2/xHNcX4qE+vIfDNXpSqnrhwA7O53R7WVQUnt8dVAIW+YHr7xTgOgGA==", "_location": "/compression", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "compression@1.8.0", "name": "compression", "escapedName": "compression", "rawSpec": "1.8.0", "saveSpec": null, "fetchSpec": "1.8.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/compression/-/compression-1.8.0.tgz", "_spec": "1.8.0", "_where": "C:\\chemistry\\portal", "bugs": {"url": "https://github.com/expressjs/compression/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "description": "Node.js compression middleware", "devDependencies": {"after": "0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0", "supertest": "6.2.3"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/compression#readme", "keywords": ["compression", "gzip", "deflate", "middleware", "express", "brotli", "http", "stream"], "license": "MIT", "name": "compression", "repository": {"type": "git", "url": "git+https://github.com/expressjs/compression.git"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.8.0"}