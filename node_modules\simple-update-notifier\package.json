{"_args": [["simple-update-notifier@1.1.0", "C:\\chemistry\\portal"]], "_development": true, "_from": "simple-update-notifier@1.1.0", "_id": "simple-update-notifier@1.1.0", "_inBundle": false, "_integrity": "sha512-VpsrsJSUcJEseSbMHkrsrAVSdvVS5I96Qo1QAQ4FxQ9wXFcB+pjj7FB7/us9+GcgfW4ziHtYMc1J0PLczb55mg==", "_location": "/simple-update-notifier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "simple-update-notifier@1.1.0", "name": "simple-update-notifier", "escapedName": "simple-update-notifier", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/nodemon"], "_resolved": "https://registry.npmmirror.com/simple-update-notifier/-/simple-update-notifier-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "dependencies": {"semver": "~7.0.0"}, "description": "Simple update notifier to check for npm updates for cli applications", "devDependencies": {"@babel/preset-env": "^7.19.1", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.0.3", "prettier": "^2.7.1", "release-it": "^15.4.2", "rollup": "^2.79.0", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.8.3"}, "engines": {"node": ">=8.10.0"}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "files": ["build", "src"], "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "license": "MIT", "main": "build/index.js", "name": "simple-update-notifier", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "scripts": {"build": "rollup -c rollup.config.js", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "prettier": "prettier --write src/**/*.ts", "prettier:check": "prettier --check src/**/*.ts", "release": "release-it", "test": "jest src --noStackTrace"}, "types": "build/index.d.ts", "version": "1.1.0"}