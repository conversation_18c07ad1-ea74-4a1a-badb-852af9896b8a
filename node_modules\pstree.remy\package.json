{"_args": [["pstree.remy@1.1.8", "C:\\chemistry\\portal"]], "_development": true, "_from": "pstree.remy@1.1.8", "_id": "pstree.remy@1.1.8", "_inBundle": false, "_integrity": "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==", "_location": "/pstree.remy", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "pstree.remy@1.1.8", "name": "pstree.remy", "escapedName": "pstree.remy", "rawSpec": "1.1.8", "saveSpec": null, "fetchSpec": "1.1.8"}, "_requiredBy": ["/nodemon"], "_resolved": "https://registry.npmmirror.com/pstree.remy/-/pstree.remy-1.1.8.tgz", "_spec": "1.1.8", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/remy/pstree/issues"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "homepage": "https://github.com/remy/pstree#readme", "keywords": ["ps", "pstree", "ps tree"], "license": "MIT", "main": "lib/index.js", "name": "pstree.remy", "prettier": {"trailingComma": "es5", "semi": true, "singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/remy/pstree.git"}, "scripts": {"_prepublish": "npm test", "test": "tap tests/*.test.js"}, "version": "1.1.8"}