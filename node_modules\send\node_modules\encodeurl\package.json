{"_args": [["encodeurl@1.0.2", "C:\\chemistry\\portal"]], "_from": "encodeurl@1.0.2", "_id": "encodeurl@1.0.2", "_inBundle": false, "_integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "_location": "/send/encodeurl", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "encodeurl@1.0.2", "name": "encodeurl", "escapedName": "encodeurl", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/send"], "_resolved": "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\chemistry\\portal", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/encodeurl#readme", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "name": "encodeurl", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/encodeurl.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.0.2"}