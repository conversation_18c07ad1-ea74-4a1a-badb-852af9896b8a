{"_from": "merge-descriptors@1.0.3", "_id": "merge-descriptors@1.0.3", "_inBundle": false, "_integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "_location": "/merge-descriptors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "merge-descriptors@1.0.3", "name": "merge-descriptors", "escapedName": "merge-descriptors", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "_shasum": "d80319a65f3c7935351e5cfdac8f9318504dbed5", "_spec": "merge-descriptors@1.0.3", "_where": "C:\\chemistry\\portal\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Merge objects using descriptors", "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "license": "MIT", "name": "merge-descriptors", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "scripts": {"lint": "eslint .", "test": "mocha test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.0.3"}