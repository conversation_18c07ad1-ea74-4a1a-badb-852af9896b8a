// 导入 Vite 的配置定义函数
import { defineConfig } from 'vite'
// 导入 Vue 插件
import vue from '@vitejs/plugin-vue'
// 导入 Node.js 路径解析模块
import { resolve } from 'path'

// Vite 官方配置文档: https://vitejs.dev/config/
export default defineConfig({
    // 插件配置
    plugins: [vue()],

    // 构建配置
    build: {
        // Rollup 打包选项
        rollupOptions: {
            // 多入口配置
            input: {
                // 客户端入口文件
                client: resolve(__dirname, 'index.html'),
                // 服务端入口文件
                server: resolve(__dirname, 'src/entry-server.js')
            }
        }
    },

    // 服务端渲染(SSR)配置
    ssr: {
        // 不外部化的依赖包，这些包会被打包到服务端代码中
        noExternal: ['vue', 'vue-router', '@vue/server-renderer'],
        // 输出格式为 ESM 模块
        format: 'esm',
        // 目标环境为 Node.js
        target: 'node',
        // 外部依赖列表（空数组表示不排除任何依赖）
        external: [],
        // 添加此配置确保 ESM 模块解析
        resolve: {
            // 外部条件，用于 Node.js 环境的模块解析
            externalConditions: ['node']
        }
    },

    // 开发服务器配置
    server: {
        // 中间件模式，用于与 Express 等服务器集成
        middlewareMode: true
    },

    // 路径别名和模块解析配置
    resolve: {
        // 路径别名设置
        alias: {
            // @ 符号指向 src 目录
            '@': resolve(__dirname, 'src'),
            // 强制使用 Vue 的 ESM 打包版本
            'vue': 'vue/dist/vue.esm-bundler.js',
            // 强制使用服务端渲染器的 ESM 打包版本
            '@vue/server-renderer': '@vue/server-renderer/dist/server-renderer.esm-bundler.js'
        },
        // 模块解析条件，按优先级排序
        conditions: ['import', 'module', 'browser', 'default']
    },

    // 定义全局变量
    define: {
        // 启用 Vue 选项式 API
        __VUE_OPTIONS_API__: true,
        // 生产环境禁用 Vue 开发工具
        __VUE_PROD_DEVTOOLS__: false
    },

    // 依赖优化配置
    optimizeDeps: {
        // 预构建包含的依赖
        include: ['vue', 'vue-router']
    }
})

