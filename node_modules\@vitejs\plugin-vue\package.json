{"_args": [["@vitejs/plugin-vue@4.6.2", "C:\\chemistry\\portal"]], "_development": true, "_from": "@vitejs/plugin-vue@4.6.2", "_id": "@vitejs/plugin-vue@4.6.2", "_inBundle": false, "_integrity": "sha512-kqf7SGFoG+80aZG6Pf+gsZIVvGSCKE98JbiWqcCV9cThtg91Jav0yvYFC9Zb+jKetNGF6ZKeoaxgZfND21fWKw==", "_location": "/@vitejs/plugin-vue", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vitejs/plugin-vue@4.6.2", "name": "@vitejs/plugin-vue", "escapedName": "@vitejs%2fplugin-vue", "scope": "@vitejs", "rawSpec": "4.6.2", "saveSpec": null, "fetchSpec": "4.6.2"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-4.6.2.tgz", "_spec": "4.6.2", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue/issues"}, "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "devDependencies": {"@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20", "debug": "^4.3.4", "rollup": "^3.29.4", "slash": "^5.1.0", "source-map-js": "^1.0.2", "vite": "^4.5.0", "vue": "^3.3.8"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "homepage": "https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "@vitejs/plugin-vue", "peerDependencies": {"vite": "^4.0.0 || ^5.0.0", "vue": "^3.2.25"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue.git", "directory": "packages/plugin-vue"}, "scripts": {"build": "unbuild && pnpm run patch-cjs", "dev": "unbuild --stub", "patch-cjs": "tsx ../../scripts/patchCJS.ts"}, "types": "./dist/index.d.ts", "version": "4.6.2"}