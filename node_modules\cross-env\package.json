{"// babel 1": "this disables all built-in plugins from kcd-scripts for tests", "// babel 2": "that way we ensure that the tests run without compilation", "// babel 3": "because this module is published as-is. It is not compiled.", "_args": [["cross-env@7.0.3", "C:\\chemistry\\portal"]], "_development": true, "_from": "cross-env@7.0.3", "_id": "cross-env@7.0.3", "_inBundle": false, "_integrity": "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==", "_location": "/cross-env", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cross-env@7.0.3", "name": "cross-env", "escapedName": "cross-env", "rawSpec": "7.0.3", "saveSpec": null, "fetchSpec": "7.0.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/cross-env/-/cross-env-7.0.3.tgz", "_spec": "7.0.3", "_where": "C:\\chemistry\\portal", "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "babel": {}, "bin": {"cross-env": "src/bin/cross-env.js", "cross-env-shell": "src/bin/cross-env-shell.js"}, "bugs": {"url": "https://github.com/kentcdodds/cross-env/issues"}, "dependencies": {"cross-spawn": "^7.0.1"}, "description": "Run scripts that set and use environment variables across platforms", "devDependencies": {"kcd-scripts": "^5.5.0"}, "engines": {"node": ">=10.14", "npm": ">=6", "yarn": ">=1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "files": ["src", "!__tests__"], "homepage": "https://github.com/kentcdodds/cross-env#readme", "keywords": ["cross-environment", "environment variable", "windows"], "license": "MIT", "main": "src/index.js", "name": "cross-env", "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/cross-env.git"}, "scripts": {"lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "version": "7.0.3"}