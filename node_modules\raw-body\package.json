{"_from": "raw-body@2.5.2", "_id": "raw-body@2.5.2", "_inBundle": false, "_integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "_location": "/raw-body", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "raw-body@2.5.2", "name": "raw-body", "escapedName": "raw-body", "rawSpec": "2.5.2", "saveSpec": null, "fetchSpec": "2.5.2"}, "_requiredBy": ["/body-parser"], "_resolved": "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.2.tgz", "_shasum": "99febd83b90e08975087e8f1f9419a149366b68a", "_spec": "raw-body@2.5.2", "_where": "C:\\chemistry\\portal\\node_modules\\body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/raw-body/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "deprecated": false, "description": "Get and validate the raw body of a readable stream.", "devDependencies": {"bluebird": "3.7.2", "eslint": "8.34.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "readable-stream": "2.3.7", "safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.d.ts", "index.js"], "homepage": "https://github.com/stream-utils/raw-body#readme", "license": "MIT", "name": "raw-body", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/raw-body.git"}, "scripts": {"lint": "eslint .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.5.2"}