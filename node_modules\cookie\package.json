{"_from": "cookie@0.7.1", "_id": "cookie@0.7.1", "_inBundle": false, "_integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "_location": "/cookie", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie@0.7.1", "name": "cookie", "escapedName": "cookie", "rawSpec": "0.7.1", "saveSpec": null, "fetchSpec": "0.7.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/cookie/-/cookie-0.7.1.tgz", "_shasum": "2f73c42142d5d5cf71310a74fc4ae61670e5dbc9", "_spec": "cookie@0.7.1", "_where": "C:\\chemistry\\portal\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "8.53.0", "eslint-plugin-markdown": "3.0.1", "mocha": "10.2.0", "nyc": "15.1.0", "safe-buffer": "5.2.1", "top-sites": "1.1.194"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "main": "index.js", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "version": "0.7.1"}