{"_from": "type-is@~1.6.18", "_id": "type-is@1.6.18", "_inBundle": false, "_integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "_location": "/type-is", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "type-is@~1.6.18", "name": "type-is", "escapedName": "type-is", "rawSpec": "~1.6.18", "saveSpec": null, "fetchSpec": "~1.6.18"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz", "_shasum": "4e552cd05df09467dcbc4ef739de89f2cf37c131", "_spec": "type-is@~1.6.18", "_where": "C:\\chemistry\\portal\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "deprecated": false, "description": "Infer the content-type of a request.", "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/type-is#readme", "keywords": ["content", "type", "checking"], "license": "MIT", "name": "type-is", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "version": "1.6.18"}