{"_args": [["cross-spawn@7.0.6", "C:\\chemistry\\portal"]], "_development": true, "_from": "cross-spawn@7.0.6", "_id": "cross-spawn@7.0.6", "_inBundle": false, "_integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "_location": "/cross-spawn", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cross-spawn@7.0.6", "name": "cross-spawn", "escapedName": "cross-spawn", "rawSpec": "7.0.6", "saveSpec": null, "fetchSpec": "7.0.6"}, "_requiredBy": ["/cross-env"], "_resolved": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "_spec": "7.0.6", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "description": "Cross platform child_process#spawn and child_process#spawnSync", "devDependencies": {"@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "babel-preset-moxy": "^3.1.0", "eslint": "^5.16.0", "eslint-config-moxy": "^7.1.0", "husky": "^3.0.5", "jest": "^24.9.0", "lint-staged": "^9.2.5", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "standard-version": "^9.5.0"}, "engines": {"node": ">= 8"}, "files": ["lib"], "homepage": "https://github.com/moxystudio/node-cross-spawn", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "main": "index.js", "name": "cross-spawn", "repository": {"type": "git", "url": "git+ssh://**************/moxystudio/node-cross-spawn.git"}, "scripts": {"lint": "eslint .", "postrelease": "git push --follow-tags origin HEAD && npm publish", "prerelease": "npm t && npm run lint", "release": "standard-version", "test": "jest --env node --coverage"}, "version": "7.0.6"}