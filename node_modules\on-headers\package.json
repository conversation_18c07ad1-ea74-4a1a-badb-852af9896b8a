{"_args": [["on-headers@1.0.2", "C:\\chemistry\\portal"]], "_from": "on-headers@1.0.2", "_id": "on-headers@1.0.2", "_inBundle": false, "_integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "_location": "/on-headers", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "on-headers@1.0.2", "name": "on-headers", "escapedName": "on-headers", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/compression"], "_resolved": "https://registry.npmmirror.com/on-headers/-/on-headers-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "description": "Execute a listener when a response is about to write headers", "devDependencies": {"eslint": "5.14.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "6.0.1", "supertest": "3.4.2"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/on-headers#readme", "keywords": ["event", "headers", "http", "onheaders"], "license": "MIT", "name": "on-headers", "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-headers.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.0.2"}