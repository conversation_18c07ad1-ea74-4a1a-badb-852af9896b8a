{"_args": [["semver@5.7.2", "C:\\chemistry\\portal"]], "_development": true, "_from": "semver@5.7.2", "_id": "semver@5.7.2", "_inBundle": false, "_integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "_location": "/semver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "semver@5.7.2", "name": "semver", "escapedName": "semver", "rawSpec": "5.7.2", "saveSpec": null, "fetchSpec": "5.7.2"}, "_requiredBy": ["/nodemon"], "_resolved": "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz", "_spec": "5.7.2", "_where": "C:\\chemistry\\portal", "author": {"name": "GitHub Inc."}, "bin": {"semver": "bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "description": "The semantic version parser used by npm.", "devDependencies": {"@npmcli/template-oss": "4.17.0", "tap": "^12.7.0"}, "files": ["bin", "range.bnf", "semver.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"lint": "echo linting disabled", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "snap": "tap test/ --100 --timeout=30", "template-oss-apply": "template-oss-apply --force", "test": "tap test/ --100 --timeout=30"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "content": "./scripts/template-oss", "version": "4.17.0"}, "version": "5.7.2"}