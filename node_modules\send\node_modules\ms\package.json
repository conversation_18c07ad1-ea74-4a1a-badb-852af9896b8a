{"_args": [["ms@2.1.3", "C:\\chemistry\\portal"]], "_from": "ms@2.1.3", "_id": "ms@2.1.3", "_inBundle": false, "_integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "_location": "/send/ms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ms@2.1.3", "name": "ms", "escapedName": "ms", "rawSpec": "2.1.3", "saveSpec": null, "fetchSpec": "2.1.3"}, "_requiredBy": ["/send"], "_resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "_spec": "2.1.3", "_where": "C:\\chemistry\\portal", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "description": "Tiny millisecond conversion utility", "devDependencies": {"eslint": "4.18.2", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1", "prettier": "2.0.5"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "files": ["index.js"], "homepage": "https://github.com/vercel/ms#readme", "license": "MIT", "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "scripts": {"lint": "eslint lib/* bin/*", "precommit": "lint-staged", "test": "mocha tests.js"}, "version": "2.1.3"}