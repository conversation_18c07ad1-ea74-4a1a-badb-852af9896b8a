{"_from": "content-disposition@0.5.4", "_id": "content-disposition@0.5.4", "_inBundle": false, "_integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "_location": "/content-disposition", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "content-disposition@0.5.4", "name": "content-disposition", "escapedName": "content-disposition", "rawSpec": "0.5.4", "saveSpec": null, "fetchSpec": "0.5.4"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz", "_shasum": "8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe", "_spec": "content-disposition@0.5.4", "_where": "C:\\chemistry\\portal\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "bundleDependencies": false, "dependencies": {"safe-buffer": "5.2.1"}, "deprecated": false, "description": "Create and parse Content-Disposition header", "devDependencies": {"deep-equal": "1.0.1", "eslint": "7.32.0", "eslint-config-standard": "13.0.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "istanbul": "0.4.5", "mocha": "9.1.3"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-disposition#readme", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "name": "content-disposition", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-disposition.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "0.5.4"}