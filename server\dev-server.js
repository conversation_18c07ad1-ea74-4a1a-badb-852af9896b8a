// 导入 Express 框架用于创建 Web 服务器
import express from 'express'
// 导入 Vite 的服务器创建函数，用于开发环境的模块热重载
import { createServer as createViteServer } from 'vite'
// 导入 Node.js 内置模块，用于处理文件 URL 路径
import { fileURLToPath } from 'url'
// 导入 Node.js 内置模块，用于处理文件路径
import { dirname, resolve } from 'path'
// 导入 Node.js 内置文件系统模块
import fs from 'fs'

// 获取当前文件所在目录的绝对路径
const __dirname = dirname(fileURLToPath(import.meta.url))
// 设置服务器端口，优先使用环境变量，默认为 3000
const port = process.env.PORT || 3000

// 异步函数：创建开发服务器
async function createServer() {
  // 创建 Express 应用实例
  const app = express()

  // 创建 Vite 开发服务器实例
  const vite = await createViteServer({
    // 配置服务器为中间件模式，不启动独立的 HTTP 服务器
    server: { middlewareMode: true },
    // 指定应用类型为自定义类型
    appType: 'custom',
    // 设置日志级别为 info
    logLevel: 'info',
    // SSR（服务端渲染）相关配置
    ssr: {
      // 指定不需要外部化的模块，这些模块会被 Vite 处理
      noExternal: ['vue', 'vue-router', '@vue/server-renderer'],
      // 指定模块格式为 ESM
      format: 'esm'
    }
  })

  // 将 Vite 的中间件集成到 Express 应用中
  app.use(vite.middlewares)

  // 处理所有路由请求的通配符中间件
  app.use('*', async (req, res) => {
    // 获取请求的原始 URL
    const url = req.originalUrl

    try {
      // 1. 同步读取 HTML 模板文件
      let template = fs.readFileSync(
        resolve(__dirname, '../index.html'),
        'utf-8'
      )

      // 2. 通过 Vite 转换 HTML 模板
      // 这会注入 Vite 的热重载客户端代码和其他插件转换
      template = await vite.transformIndexHtml(url, template)

      // 3. 加载服务端入口模块
      // vite.ssrLoadModule 会自动转换 ESM 代码使其在 Node.js 中运行
      const { render } = await vite.ssrLoadModule('/src/entry-server.js')

      // 4. 调用渲染函数生成应用的 HTML 内容
      // 这里假设 entry-server.js 导出的 render 函数会调用相应的 SSR API
      const { html: appHtml } = await render(url)

      // 5. 将渲染后的应用 HTML 注入到模板的指定位置
      const html = template.replace(`<!--ssr-outlet-->`, appHtml)

      // 6. 设置响应头并返回完整的 HTML 页面
      res.status(200).set({ 'Content-Type': 'text/html' }).end(html)
    } catch (e) {
      // 如果发生错误，使用 Vite 修复错误堆栈信息
      // 这样可以将错误映射回原始源代码位置
      vite.ssrFixStacktrace(e)
      // 在控制台输出错误信息
      console.error(e)
      // 返回 500 错误状态和错误消息
      res.status(500).end(e.message)
    }
  })

  // 启动服务器并监听指定端口
  app.listen(port, () => {
    // 输出服务器启动成功的提示信息
    console.log(`🚀 开发服务器运行在 http://localhost:${port}`)
    console.log(`📝 使用 Vite 进行热重载开发`)
  })
}

// 调用函数启动服务器
createServer()

