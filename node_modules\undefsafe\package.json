{"_args": [["undefsafe@2.0.5", "C:\\chemistry\\portal"]], "_development": true, "_from": "undefsafe@2.0.5", "_id": "undefsafe@2.0.5", "_inBundle": false, "_integrity": "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==", "_location": "/undefsafe", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "undefsafe@2.0.5", "name": "undefsafe", "escapedName": "undefsafe", "rawSpec": "2.0.5", "saveSpec": null, "fetchSpec": "2.0.5"}, "_requiredBy": ["/nodemon"], "_resolved": "https://registry.npmmirror.com/undefsafe/-/undefsafe-2.0.5.tgz", "_spec": "2.0.5", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "dependencies": {}, "description": "Undefined safe way of extracting object properties", "devDependencies": {"semantic-release": "^18.0.0", "tap": "^5.7.1", "tap-only": "0.0.5"}, "directories": {"test": "test"}, "homepage": "https://github.com/remy/undefsafe#readme", "keywords": ["undefined"], "license": "MIT", "main": "lib/undefsafe.js", "name": "undefsafe", "prettier": {"trailingComma": "none", "singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "scripts": {"cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release", "test": "tap test/**/*.test.js -R spec"}, "tonicExampleFilename": "example.js", "version": "2.0.5"}