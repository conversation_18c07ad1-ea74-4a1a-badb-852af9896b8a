{"_args": [["path-key@3.1.1", "C:\\chemistry\\portal"]], "_development": true, "_from": "path-key@3.1.1", "_id": "path-key@3.1.1", "_inBundle": false, "_integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "_location": "/path-key", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path-key@3.1.1", "name": "path-key", "escapedName": "path-key", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/cross-spawn"], "_resolved": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "_spec": "3.1.1", "_where": "C:\\chemistry\\portal", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "description": "Get the PATH environment variable key cross-platform", "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/path-key#readme", "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "license": "MIT", "name": "path-key", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.1"}